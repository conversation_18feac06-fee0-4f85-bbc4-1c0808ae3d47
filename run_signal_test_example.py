#!/usr/bin/env python3
"""
信号一致性测试运行示例

这个脚本展示了如何使用信号一致性测试工具，
包括基本用法、错误处理和结果分析
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def run_quick_test_example():
    """运行快速测试示例"""
    print("🚀 运行快速信号一致性测试示例")
    print("=" * 50)
    
    try:
        # 导入快速测试模块
        from quick_signal_test import test_ben_graham_quick, test_peter_lynch_quick, print_result
        
        # 测试单个股票的Ben <PERSON>代理
        print("\n📊 测试 Ben Graham 代理 (AAPL)")
        ben_result = test_ben_graham_quick("AAPL")
        print_result("<PERSON> Graham", "AAPL", ben_result)
        
        # 测试单个股票的<PERSON>代理
        print("\n📊 测试 <PERSON> 代理 (AAPL)")
        lynch_result = test_peter_lynch_quick("AAPL")
        print_result("<PERSON>", "AAPL", lynch_result)
        
        # 分析结果
        print("\n📈 结果分析:")
        print("-" * 30)
        
        if "error" not in ben_result:
            ben_consistent = ben_result.get("consistent", False)
            print(f"Ben Graham 一致性: {'✅ 是' if ben_consistent else '❌ 否'}")
            if not ben_consistent:
                print(f"  计算: {ben_result.get('calculated_signal', '未知')}")
                print(f"  LLM: {ben_result.get('llm_signal', '未知')}")
        
        if "error" not in lynch_result:
            lynch_consistent = lynch_result.get("consistent", False)
            print(f"Peter Lynch 一致性: {'✅ 是' if lynch_consistent else '❌ 否'}")
            if not lynch_consistent:
                print(f"  计算: {lynch_result.get('calculated_signal', '未知')}")
                print(f"  LLM: {lynch_result.get('llm_signal', '未知')}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保在项目根目录运行此脚本")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def run_batch_test_example():
    """运行批量测试示例"""
    print("\n🚀 运行批量信号一致性测试示例")
    print("=" * 50)
    
    try:
        # 导入完整测试模块
        from test_signal_consistency import SignalConsistencyTester
        
        # 创建测试器
        tester = SignalConsistencyTester(
            test_tickers=["AAPL", "MSFT"],  # 测试两只股票
            test_date=None  # 使用默认日期
        )
        
        print("📊 开始批量测试...")
        
        # 运行测试
        results = tester.test_all_agents()
        
        # 生成报告
        report = tester.generate_report(results)
        
        # 显示报告
        print("\n📄 测试报告:")
        print(report)
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 批量测试失败: {e}")
        return False

def analyze_consistency_patterns():
    """分析一致性模式"""
    print("\n🔍 分析一致性模式示例")
    print("=" * 50)
    
    # 这里可以添加更复杂的分析逻辑
    # 比如分析不同股票、不同市场条件下的一致性模式
    
    test_cases = [
        {"ticker": "AAPL", "expected": "通常一致性较高"},
        {"ticker": "MSFT", "expected": "基本面强劲，一致性好"},
        {"ticker": "NVDA", "expected": "成长股，可能存在分歧"}
    ]
    
    print("📈 预期一致性模式:")
    for case in test_cases:
        print(f"  {case['ticker']}: {case['expected']}")
    
    print("\n💡 分析建议:")
    print("  1. 价值股(如AAPL)通常计算与LLM一致性较高")
    print("  2. 成长股可能存在更多主观判断差异")
    print("  3. 市场波动期间一致性可能降低")
    print("  4. 关注边界情况(评分接近阈值)")

def demonstrate_error_handling():
    """演示错误处理"""
    print("\n🛠️ 错误处理示例")
    print("=" * 50)
    
    # 演示各种可能的错误情况
    error_scenarios = [
        {
            "scenario": "无效股票代码",
            "ticker": "INVALID",
            "expected": "API数据获取失败"
        },
        {
            "scenario": "网络连接问题", 
            "ticker": "AAPL",
            "expected": "超时或连接错误"
        },
        {
            "scenario": "API配额耗尽",
            "ticker": "AAPL", 
            "expected": "API限制错误"
        }
    ]
    
    print("🚨 常见错误场景:")
    for scenario in error_scenarios:
        print(f"  {scenario['scenario']}: {scenario['expected']}")
    
    print("\n🔧 故障排除步骤:")
    print("  1. 检查网络连接")
    print("  2. 验证API密钥配置")
    print("  3. 确认股票代码有效")
    print("  4. 检查API配额状态")
    print("  5. 查看详细错误日志")

def show_usage_tips():
    """显示使用技巧"""
    print("\n💡 使用技巧")
    print("=" * 50)
    
    tips = [
        "开发时使用quick_signal_test.py进行快速验证",
        "生产环境使用test_signal_consistency.py进行全面测试",
        "定期运行测试以监控模型一致性",
        "关注不一致的案例，可能揭示模型改进机会",
        "保存测试结果用于历史对比分析",
        "在修改代理逻辑后立即运行测试",
        "使用不同市场条件下的数据进行测试"
    ]
    
    for i, tip in enumerate(tips, 1):
        print(f"  {i}. {tip}")

def main():
    """主函数 - 运行所有示例"""
    print("🎯 投资大师代理信号一致性测试 - 运行示例")
    print("=" * 60)
    print(f"⏰ 运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 显示使用技巧
    show_usage_tips()
    
    # 演示错误处理
    demonstrate_error_handling()
    
    # 分析一致性模式
    analyze_consistency_patterns()
    
    # 询问用户是否要运行实际测试
    print("\n" + "=" * 60)
    response = input("🤔 是否要运行实际的信号一致性测试? (y/N): ").strip().lower()
    
    if response in ['y', 'yes', '是']:
        print("\n选择测试类型:")
        print("1. 快速测试 (推荐)")
        print("2. 批量测试")
        
        choice = input("请选择 (1/2): ").strip()
        
        if choice == "1":
            success = run_quick_test_example()
        elif choice == "2":
            success = run_batch_test_example()
        else:
            print("❌ 无效选择")
            success = False
        
        if success:
            print("\n✅ 测试完成!")
        else:
            print("\n❌ 测试失败，请检查配置和网络连接")
    else:
        print("\n📝 示例演示完成。要运行实际测试，请使用:")
        print("  python quick_signal_test.py  # 快速测试")
        print("  python test_signal_consistency.py  # 完整测试")
    
    print(f"\n⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
