#!/usr/bin/env python3
"""
快速信号一致性测试脚本

专门测试<PERSON>和Peter <PERSON>代理的计算信号与LLM推理信号是否一致
这是一个简化版本，便于快速验证和调试
"""

import sys
import os
import json
from datetime import datetime, timedelta
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def print_header(title: str):
    """打印标题"""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print(f"{'='*60}")

def print_result(agent_name: str, ticker: str, result: Dict[str, Any]):
    """打印测试结果"""
    print(f"\n📊 {agent_name} - {ticker} 测试结果:")
    print("-" * 40)
    
    if "error" in result:
        print(f"❌ 错误: {result['error']}")
        return
    
    calculated = result.get("calculated_signal", "未知")
    llm = result.get("llm_signal", "未知")
    consistent = result.get("consistent", False)
    
    status = "✅ 一致" if consistent else "❌ 不一致"
    print(f"{status}")
    print(f"  计算信号: {calculated}")
    print(f"  LLM信号: {llm}")
    
    if "calculated_score" in result:
        score = result["calculated_score"]
        max_score = result.get("max_score", 1)
        ratio = result.get("score_ratio", 0)
        print(f"  计算评分: {score:.2f}/{max_score:.2f} ({ratio:.1%})")
    
    if "llm_confidence" in result:
        print(f"  LLM置信度: {result['llm_confidence']}%")
    
    if not consistent and "llm_reasoning" in result:
        print(f"  LLM推理: {result['llm_reasoning']}")

def test_ben_graham_quick(ticker: str = "AAPL") -> Dict[str, Any]:
    """快速测试Ben Graham代理"""
    try:
        from src.agents.ben_graham import analyze_earnings, analyze_financial_strength, analyze_valuation
        from src.tools.api import get_financial_metrics, get_market_cap, get_current_price
        
        print(f"📈 获取 {ticker} 的财务数据...")
        
        # 获取数据
        end_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        financial_metrics = get_financial_metrics(ticker, end_date, "ttm", 10, "test_ben_graham")
        
        if not financial_metrics:
            return {"error": "无法获取财务数据"}
        
        metrics = financial_metrics[0]
        market_cap = get_market_cap(ticker, end_date, "test_ben_graham")
        current_price = get_current_price(ticker, end_date, "test_ben_graham")
        
        print("📊 执行Ben Graham分析计算...")
        
        # 手动计算
        earnings_analysis = analyze_earnings(metrics)
        strength_analysis = analyze_financial_strength(metrics)
        valuation_analysis = analyze_valuation(metrics, market_cap, current_price)
        
        total_score = (
            earnings_analysis["score"] + 
            strength_analysis["score"] + 
            valuation_analysis["score"]
        )
        max_possible_score = (
            earnings_analysis["max_score"] + 
            strength_analysis["max_score"] + 
            valuation_analysis["max_score"]
        )
        
        # 计算信号
        if total_score >= 0.7 * max_possible_score:
            calculated_signal = "bullish"
        elif total_score <= 0.3 * max_possible_score:
            calculated_signal = "bearish"
        else:
            calculated_signal = "neutral"
        
        print("🤖 运行Ben Graham LLM代理...")
        
        # 运行LLM代理
        from src.agents.ben_graham import ben_graham_agent
        from src.graph.state import AgentState
        
        state = {
            "data": {
                "tickers": [ticker],
                "start_date": "2024-01-01", 
                "end_date": end_date,
                "analyst_signals": {}
            },
            "metadata": {
                "model_name": "meta-llama/llama-4-scout:free",
                "model_provider": "openrouter",
                "show_reasoning": False
            },
            "messages": []
        }
        
        ben_graham_agent(state)
        
        if ticker in state["data"]["analyst_signals"].get("ben_graham_agent", {}):
            agent_result = state["data"]["analyst_signals"]["ben_graham_agent"][ticker]
            llm_signal = agent_result["signal"]
            llm_confidence = agent_result.get("confidence", 0)
            llm_reasoning = agent_result.get("reasoning", "")
        else:
            return {"error": "Ben Graham LLM代理执行失败"}
        
        return {
            "calculated_signal": calculated_signal,
            "llm_signal": llm_signal,
            "consistent": calculated_signal == llm_signal,
            "calculated_score": total_score,
            "max_score": max_possible_score,
            "score_ratio": total_score / max_possible_score if max_possible_score > 0 else 0,
            "llm_confidence": llm_confidence,
            "llm_reasoning": llm_reasoning[:150] + "..." if len(llm_reasoning) > 150 else llm_reasoning,
            "details": {
                "earnings_score": f"{earnings_analysis['score']}/{earnings_analysis['max_score']}",
                "strength_score": f"{strength_analysis['score']}/{strength_analysis['max_score']}",
                "valuation_score": f"{valuation_analysis['score']}/{valuation_analysis['max_score']}"
            }
        }
        
    except Exception as e:
        return {"error": f"测试失败: {str(e)}"}

def test_peter_lynch_quick(ticker: str = "AAPL") -> Dict[str, Any]:
    """快速测试Peter Lynch代理"""
    try:
        from src.agents.peter_lynch import (
            analyze_growth, analyze_valuation, analyze_fundamentals, 
            analyze_sentiment, analyze_insider_activity
        )
        from src.tools.api import (
            get_financial_metrics, get_market_cap, get_current_price,
            get_insider_trades, get_company_news
        )
        
        print(f"📈 获取 {ticker} 的数据...")
        
        # 获取数据
        end_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        financial_metrics = get_financial_metrics(ticker, end_date, "ttm", 10, "test_peter_lynch")
        
        if not financial_metrics:
            return {"error": "无法获取财务数据"}
        
        metrics = financial_metrics[0]
        market_cap = get_market_cap(ticker, end_date, "test_peter_lynch")
        current_price = get_current_price(ticker, end_date, "test_peter_lynch")
        insider_trades = get_insider_trades(ticker, end_date, "test_peter_lynch")
        news = get_company_news(ticker, end_date, "test_peter_lynch")
        
        print("📊 执行Peter Lynch分析计算...")
        
        # 手动计算各部分
        growth_analysis = analyze_growth(metrics)
        valuation_analysis = analyze_valuation(metrics, market_cap, current_price)
        fundamentals_analysis = analyze_fundamentals(metrics)
        sentiment_analysis = analyze_sentiment(news)
        insider_activity = analyze_insider_activity(insider_trades)
        
        # 加权计算总分
        total_score = (
            growth_analysis["score"] * 0.30
            + valuation_analysis["score"] * 0.25
            + fundamentals_analysis["score"] * 0.20
            + sentiment_analysis["score"] * 0.15
            + insider_activity["score"] * 0.10
        )
        
        # 计算信号
        if total_score >= 7.5:
            calculated_signal = "bullish"
        elif total_score <= 4.5:
            calculated_signal = "bearish"
        else:
            calculated_signal = "neutral"
        
        print("🤖 运行Peter Lynch LLM代理...")
        
        # 运行LLM代理
        from src.agents.peter_lynch import peter_lynch_agent
        
        state = {
            "data": {
                "tickers": [ticker],
                "start_date": "2024-01-01",
                "end_date": end_date,
                "analyst_signals": {}
            },
            "metadata": {
                "model_name": "meta-llama/llama-4-scout:free",
                "model_provider": "openrouter", 
                "show_reasoning": False
            },
            "messages": []
        }
        
        peter_lynch_agent(state)
        
        if ticker in state["data"]["analyst_signals"].get("peter_lynch_agent", {}):
            agent_result = state["data"]["analyst_signals"]["peter_lynch_agent"][ticker]
            llm_signal = agent_result["signal"]
            llm_confidence = agent_result.get("confidence", 0)
            llm_reasoning = agent_result.get("reasoning", "")
        else:
            return {"error": "Peter Lynch LLM代理执行失败"}
        
        return {
            "calculated_signal": calculated_signal,
            "llm_signal": llm_signal,
            "consistent": calculated_signal == llm_signal,
            "calculated_score": total_score,
            "max_score": 10.0,
            "score_ratio": total_score / 10.0,
            "llm_confidence": llm_confidence,
            "llm_reasoning": llm_reasoning[:150] + "..." if len(llm_reasoning) > 150 else llm_reasoning,
            "details": {
                "growth_score": f"{growth_analysis['score']:.1f}/10",
                "valuation_score": f"{valuation_analysis['score']:.1f}/10", 
                "fundamentals_score": f"{fundamentals_analysis['score']:.1f}/10",
                "sentiment_score": f"{sentiment_analysis['score']:.1f}/10",
                "insider_score": f"{insider_activity['score']:.1f}/10"
            }
        }
        
    except Exception as e:
        return {"error": f"测试失败: {str(e)}"}

def main():
    """主函数"""
    print_header("投资大师代理信号一致性快速测试")
    
    # 测试股票列表
    test_tickers = ["AAPL", "MSFT"]
    
    print(f"📅 测试日期: {(datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')}")
    print(f"📊 测试股票: {', '.join(test_tickers)}")
    
    all_results = {}
    
    for ticker in test_tickers:
        print_header(f"测试股票: {ticker}")
        
        # 测试Ben Graham
        print("\n🔍 测试Ben Graham代理...")
        ben_result = test_ben_graham_quick(ticker)
        print_result("Ben Graham", ticker, ben_result)
        
        # 测试Peter Lynch
        print("\n🔍 测试Peter Lynch代理...")
        lynch_result = test_peter_lynch_quick(ticker)
        print_result("Peter Lynch", ticker, lynch_result)
        
        all_results[ticker] = {
            "ben_graham": ben_result,
            "peter_lynch": lynch_result
        }
    
    # 生成简要统计
    print_header("测试统计")
    
    total_tests = 0
    consistent_tests = 0
    
    for ticker, results in all_results.items():
        for agent, result in results.items():
            if "error" not in result:
                total_tests += 1
                if result.get("consistent", False):
                    consistent_tests += 1
    
    consistency_rate = consistent_tests / total_tests if total_tests > 0 else 0
    print(f"📊 总测试数: {total_tests}")
    print(f"✅ 一致测试数: {consistent_tests}")
    print(f"📈 一致性比率: {consistency_rate:.1%}")
    
    # 保存结果
    results_file = f"quick_signal_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    print(f"\n📄 详细结果已保存到: {results_file}")

if __name__ == "__main__":
    main()
