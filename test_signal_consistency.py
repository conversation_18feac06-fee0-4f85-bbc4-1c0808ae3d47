#!/usr/bin/env python3
"""
投资大师代理信号一致性测试脚本

测试计算得到的信号与LLM推理后输出的信号是否一致
支持的投资大师代理：
- <PERSON>
- <PERSON>  
- <PERSON>
- <PERSON>
- <PERSON>
"""

import sys
import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.agents.ben_graham import ben_graham_agent, analyze_earnings, analyze_financial_strength, analyze_valuation
from src.agents.peter_lynch import peter_lynch_agent, analyze_growth, analyze_valuation as lynch_valuation, analyze_fundamentals, analyze_sentiment, analyze_insider_activity
from src.agents.warren_buffett import warren_buffett_agent
from src.agents.michael_burry import michael_burry_agent
from src.agents.phil_fisher import phil_fisher_agent
from src.graph.state import AgentState
from src.tools.api import get_financial_metrics, get_market_cap, get_current_price, get_insider_trades, get_company_news, get_dynamic_market_context
from src.utils.progress import progress


class SignalConsistencyTester:
    """投资大师代理信号一致性测试器"""
    
    def __init__(self, test_tickers: List[str] = None, test_date: str = None):
        """
        初始化测试器
        
        Args:
            test_tickers: 测试股票列表，默认使用 ['AAPL', 'MSFT', 'NVDA']
            test_date: 测试日期，默认使用昨天
        """
        self.test_tickers = test_tickers or ['AAPL', 'MSFT', 'NVDA']
        self.test_date = test_date or (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        self.results = {}
        
    def create_test_state(self, ticker: str) -> AgentState:
        """创建测试用的AgentState"""
        return {
            "data": {
                "tickers": [ticker],
                "start_date": "2024-01-01",
                "end_date": self.test_date,
                "analyst_signals": {}
            },
            "metadata": {
                "model_name": "meta-llama/llama-4-scout:free",
                "model_provider": "openrouter",
                "show_reasoning": False
            },
            "messages": []
        }
    
    def test_ben_graham_consistency(self, ticker: str) -> Dict[str, Any]:
        """测试Ben Graham代理的信号一致性"""
        print(f"\n🔍 测试 Ben Graham 代理 - {ticker}")
        
        try:
            # 获取必要数据
            financial_metrics = get_financial_metrics(ticker, self.test_date, "ttm", 10, "test_ben_graham")
            if not financial_metrics:
                return {"error": "无法获取财务数据"}
            
            metrics = financial_metrics[0]
            market_cap = get_market_cap(ticker, self.test_date, "test_ben_graham")
            current_price = get_current_price(ticker, self.test_date, "test_ben_graham")
            market_context = get_dynamic_market_context(ticker, self.test_date, "test_ben_graham")
            
            # 手动计算各部分分析
            earnings_analysis = analyze_earnings(metrics)
            strength_analysis = analyze_financial_strength(metrics)
            valuation_analysis = analyze_valuation(metrics, market_cap, current_price)
            
            # 计算总分和信号
            total_score = (
                earnings_analysis["score"] + 
                strength_analysis["score"] + 
                valuation_analysis["score"]
            )
            max_possible_score = (
                earnings_analysis["max_score"] + 
                strength_analysis["max_score"] + 
                valuation_analysis["max_score"]
            )
            
            # 根据评分确定计算信号
            if total_score >= 0.7 * max_possible_score:
                calculated_signal = "bullish"
            elif total_score <= 0.3 * max_possible_score:
                calculated_signal = "bearish"
            else:
                calculated_signal = "neutral"
            
            # 运行完整的代理获取LLM推理信号
            state = self.create_test_state(ticker)
            result_state = ben_graham_agent(state)
            
            if ticker in state["data"]["analyst_signals"].get("ben_graham_agent", {}):
                llm_signal = state["data"]["analyst_signals"]["ben_graham_agent"][ticker]["signal"]
                llm_confidence = state["data"]["analyst_signals"]["ben_graham_agent"][ticker].get("confidence", 0)
                llm_reasoning = state["data"]["analyst_signals"]["ben_graham_agent"][ticker].get("reasoning", "")
            else:
                return {"error": "LLM代理执行失败"}
            
            return {
                "calculated_signal": calculated_signal,
                "llm_signal": llm_signal,
                "consistent": calculated_signal == llm_signal,
                "calculated_score": total_score,
                "max_score": max_possible_score,
                "score_ratio": total_score / max_possible_score if max_possible_score > 0 else 0,
                "llm_confidence": llm_confidence,
                "llm_reasoning": llm_reasoning[:200] + "..." if len(llm_reasoning) > 200 else llm_reasoning,
                "details": {
                    "earnings_analysis": earnings_analysis,
                    "strength_analysis": strength_analysis,
                    "valuation_analysis": valuation_analysis
                }
            }
            
        except Exception as e:
            return {"error": f"测试失败: {str(e)}"}
    
    def test_peter_lynch_consistency(self, ticker: str) -> Dict[str, Any]:
        """测试Peter Lynch代理的信号一致性"""
        print(f"\n🔍 测试 Peter Lynch 代理 - {ticker}")
        
        try:
            # 获取必要数据
            financial_metrics = get_financial_metrics(ticker, self.test_date, "ttm", 10, "test_peter_lynch")
            if not financial_metrics:
                return {"error": "无法获取财务数据"}
            
            metrics = financial_metrics[0]
            market_cap = get_market_cap(ticker, self.test_date, "test_peter_lynch")
            current_price = get_current_price(ticker, self.test_date, "test_peter_lynch")
            market_context = get_dynamic_market_context(ticker, self.test_date, "test_peter_lynch")
            insider_trades = get_insider_trades(ticker, self.test_date, "test_peter_lynch")
            news = get_company_news(ticker, self.test_date, "test_peter_lynch")
            
            # 手动计算各部分分析
            growth_analysis = analyze_growth(metrics)
            valuation_analysis = lynch_valuation(metrics, market_cap, current_price)
            fundamentals_analysis = analyze_fundamentals(metrics)
            sentiment_analysis = analyze_sentiment(news)
            insider_activity = analyze_insider_activity(insider_trades)
            
            # 计算加权总分
            total_score = (
                growth_analysis["score"] * 0.30
                + valuation_analysis["score"] * 0.25
                + fundamentals_analysis["score"] * 0.20
                + sentiment_analysis["score"] * 0.15
                + insider_activity["score"] * 0.10
            )
            
            # 根据评分确定计算信号
            if total_score >= 7.5:
                calculated_signal = "bullish"
            elif total_score <= 4.5:
                calculated_signal = "bearish"
            else:
                calculated_signal = "neutral"
            
            # 运行完整的代理获取LLM推理信号
            state = self.create_test_state(ticker)
            result_state = peter_lynch_agent(state)
            
            if ticker in state["data"]["analyst_signals"].get("peter_lynch_agent", {}):
                llm_signal = state["data"]["analyst_signals"]["peter_lynch_agent"][ticker]["signal"]
                llm_confidence = state["data"]["analyst_signals"]["peter_lynch_agent"][ticker].get("confidence", 0)
                llm_reasoning = state["data"]["analyst_signals"]["peter_lynch_agent"][ticker].get("reasoning", "")
            else:
                return {"error": "LLM代理执行失败"}
            
            return {
                "calculated_signal": calculated_signal,
                "llm_signal": llm_signal,
                "consistent": calculated_signal == llm_signal,
                "calculated_score": total_score,
                "max_score": 10.0,
                "score_ratio": total_score / 10.0,
                "llm_confidence": llm_confidence,
                "llm_reasoning": llm_reasoning[:200] + "..." if len(llm_reasoning) > 200 else llm_reasoning,
                "details": {
                    "growth_analysis": growth_analysis,
                    "valuation_analysis": valuation_analysis,
                    "fundamentals_analysis": fundamentals_analysis,
                    "sentiment_analysis": sentiment_analysis,
                    "insider_activity": insider_activity
                }
            }
            
        except Exception as e:
            return {"error": f"测试失败: {str(e)}"}

    def test_warren_buffett_consistency(self, ticker: str) -> Dict[str, Any]:
        """测试Warren Buffett代理的信号一致性"""
        print(f"\n🔍 测试 Warren Buffett 代理 - {ticker}")

        try:
            # 运行完整的代理获取LLM推理信号
            state = self.create_test_state(ticker)
            result_state = warren_buffett_agent(state)

            if ticker in state["data"]["analyst_signals"].get("warren_buffett_agent", {}):
                llm_signal = state["data"]["analyst_signals"]["warren_buffett_agent"][ticker]["signal"]
                llm_confidence = state["data"]["analyst_signals"]["warren_buffett_agent"][ticker].get("confidence", 0)
                llm_reasoning = state["data"]["analyst_signals"]["warren_buffett_agent"][ticker].get("reasoning", "")

                # Warren Buffett代理的计算逻辑比较复杂，这里简化处理
                # 实际应该从代理内部获取计算信号，但由于代码结构限制，暂时标记为需要手动验证
                return {
                    "calculated_signal": "需要手动验证",
                    "llm_signal": llm_signal,
                    "consistent": "需要手动验证",
                    "llm_confidence": llm_confidence,
                    "llm_reasoning": llm_reasoning[:200] + "..." if len(llm_reasoning) > 200 else llm_reasoning,
                    "note": "Warren Buffett代理计算逻辑复杂，需要手动验证一致性"
                }
            else:
                return {"error": "LLM代理执行失败"}

        except Exception as e:
            return {"error": f"测试失败: {str(e)}"}

    def test_michael_burry_consistency(self, ticker: str) -> Dict[str, Any]:
        """测试Michael Burry代理的信号一致性"""
        print(f"\n🔍 测试 Michael Burry 代理 - {ticker}")

        try:
            # 运行完整的代理获取LLM推理信号
            state = self.create_test_state(ticker)
            result_state = michael_burry_agent(state)

            if ticker in state["data"]["analyst_signals"].get("michael_burry_agent", {}):
                llm_signal = state["data"]["analyst_signals"]["michael_burry_agent"][ticker]["signal"]
                llm_confidence = state["data"]["analyst_signals"]["michael_burry_agent"][ticker].get("confidence", 0)
                llm_reasoning = state["data"]["analyst_signals"]["michael_burry_agent"][ticker].get("reasoning", "")

                # Michael Burry代理的计算逻辑也比较复杂，这里简化处理
                return {
                    "calculated_signal": "需要手动验证",
                    "llm_signal": llm_signal,
                    "consistent": "需要手动验证",
                    "llm_confidence": llm_confidence,
                    "llm_reasoning": llm_reasoning[:200] + "..." if len(llm_reasoning) > 200 else llm_reasoning,
                    "note": "Michael Burry代理计算逻辑复杂，需要手动验证一致性"
                }
            else:
                return {"error": "LLM代理执行失败"}

        except Exception as e:
            return {"error": f"测试失败: {str(e)}"}

    def test_all_agents(self) -> Dict[str, Dict[str, Any]]:
        """测试所有支持的投资大师代理"""
        print(f"🚀 开始投资大师代理信号一致性测试")
        print(f"📅 测试日期: {self.test_date}")
        print(f"📊 测试股票: {', '.join(self.test_tickers)}")
        
        all_results = {}
        
        for ticker in self.test_tickers:
            print(f"\n{'='*60}")
            print(f"📈 测试股票: {ticker}")
            print(f"{'='*60}")
            
            ticker_results = {}
            
            # 测试Ben Graham
            ticker_results["ben_graham"] = self.test_ben_graham_consistency(ticker)
            
            # 测试Peter Lynch
            ticker_results["peter_lynch"] = self.test_peter_lynch_consistency(ticker)
            
            # 测试Warren Buffett
            ticker_results["warren_buffett"] = self.test_warren_buffett_consistency(ticker)

            # 测试Michael Burry
            ticker_results["michael_burry"] = self.test_michael_burry_consistency(ticker)
            
            all_results[ticker] = ticker_results
        
        return all_results
    
    def generate_report(self, results: Dict[str, Dict[str, Any]]) -> str:
        """生成测试报告"""
        report = []
        report.append("📊 投资大师代理信号一致性测试报告")
        report.append("=" * 60)
        report.append(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"测试日期: {self.test_date}")
        report.append(f"测试股票: {', '.join(self.test_tickers)}")
        report.append("")
        
        # 统计总体一致性
        total_tests = 0
        consistent_tests = 0
        
        for ticker, ticker_results in results.items():
            report.append(f"📈 {ticker} 测试结果:")
            report.append("-" * 40)
            
            for agent_name, agent_result in ticker_results.items():
                if "error" in agent_result:
                    report.append(f"  ❌ {agent_name}: {agent_result['error']}")
                    continue
                
                total_tests += 1
                is_consistent = agent_result.get("consistent", False)
                if is_consistent:
                    consistent_tests += 1
                
                status = "✅ 一致" if is_consistent else "❌ 不一致"
                report.append(f"  {status} {agent_name}:")
                report.append(f"    计算信号: {agent_result['calculated_signal']}")
                report.append(f"    LLM信号: {agent_result['llm_signal']}")
                report.append(f"    计算评分: {agent_result['calculated_score']:.2f}/{agent_result['max_score']:.2f} ({agent_result['score_ratio']:.1%})")
                report.append(f"    LLM置信度: {agent_result['llm_confidence']}%")
                if not is_consistent:
                    report.append(f"    LLM推理: {agent_result['llm_reasoning']}")
                report.append("")
        
        # 总体统计
        consistency_rate = consistent_tests / total_tests if total_tests > 0 else 0
        report.append("📊 总体统计:")
        report.append("-" * 40)
        report.append(f"总测试数: {total_tests}")
        report.append(f"一致测试数: {consistent_tests}")
        report.append(f"一致性比率: {consistency_rate:.1%}")
        
        return "\n".join(report)


def main():
    """主函数"""
    # 可以通过命令行参数自定义测试
    import argparse
    
    parser = argparse.ArgumentParser(description="投资大师代理信号一致性测试")
    parser.add_argument("--tickers", nargs="+", default=["AAPL", "MSFT"], help="测试股票列表")
    parser.add_argument("--date", type=str, help="测试日期 (YYYY-MM-DD)")
    parser.add_argument("--output", type=str, help="输出报告文件路径")
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = SignalConsistencyTester(
        test_tickers=args.tickers,
        test_date=args.date
    )
    
    # 运行测试
    results = tester.test_all_agents()
    
    # 生成报告
    report = tester.generate_report(results)
    
    # 输出报告
    print("\n" + report)
    
    # 保存报告到文件
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"\n📄 报告已保存到: {args.output}")
    
    # 保存详细结果到JSON
    results_file = f"signal_consistency_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    print(f"📄 详细结果已保存到: {results_file}")


if __name__ == "__main__":
    main()
